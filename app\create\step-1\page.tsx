"use client";
import React, { useEffect, useState, useRef } from "react";
import { useRouter } from "next/navigation";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { get_all_companies } from "@/lib/company"; // Adjust the import path as necessary
import "react-circular-progressbar/dist/styles.css";
import uploadIcon from "../../assets/images/icons/upload-icon.svg";
import arrowRightIcon from "../../assets/images/icons/right-arrow.svg";
import Image from "next/image";
import SiteStepper from "@/app/components/ui/SiteStepper";

const totalSteps = 4;

// Helper to get meta value by key
function getMetaValue(metas: any[], key: string) {
  return metas?.find((meta) => meta.meta_key === key)?.meta_value || "";
}

export default function Step1() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [companyTab, setCompanyTab] = useState("existing");
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [faviconFile, setFaviconFile] = useState<File | null>(null);
  const [logoError, setLogoError] = useState("");
  const [faviconError, setFaviconError] = useState("");
  const logoInputRef = useRef<HTMLInputElement>(null);
  const faviconInputRef = useRef<HTMLInputElement>(null);
  const [form, setForm] = useState({
    domain: "",
    siteName: "",
    tagline: "",
    company: "",
    companyName: "",
    phone: "",
    email: "",
    address: "",
  });
  const [formError, setFormError] = useState("");
  const [submitting, setSubmitting] = useState(false);
  const [companies, setCompanies] = useState<
    {
      id: number;
      company: string;
      email: string;
      phone: string;
      address: string;
    }[]
  >([]);
  const [step, setStep] = useState(1); // New state for step

  useEffect(() => {
    // ...existing token check...
    get_all_companies().then(setCompanies);
    setLoading(false);
  }, [router]);

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        Loading...
      </div>
    );
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setForm((prev) => ({ ...prev, [id]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError("");
    const validation = validateForm();
    if (validation) {
      setFormError(validation);
      return;
    }
    setSubmitting(true);
    try {
      const fd = new FormData();
      fd.append("domain", form.domain.trim());
      fd.append("siteName", form.siteName.trim());
      fd.append("tagline", form.tagline.trim());
      fd.append("logo", logoFile!);
      fd.append("favicon", faviconFile!);
      if (companyTab === "existing") {
        fd.append("company", form.company);
      } else {
        fd.append("companyName", form.companyName.trim());
      }
      fd.append("phone", form.phone.trim());
      fd.append("email", form.email.trim());
      fd.append("address", form.address.trim());
      const res = await fetch("/api/site/create", {
        method: "POST",
        body: fd,
      });
      if (!res.ok) {
        const data = await res.json().catch(() => ({}));
        setFormError(data.error || "Failed to create site.");
        setSubmitting(false);
        return;
      }
      const data = await res.json();
      router.push(
        `/create/step-2?${new URLSearchParams({ siteId: data.siteId })}`
      );
    } catch (err) {
      setFormError("Failed to create site.");
    } finally {
      setSubmitting(false);
    }
  };

  const handleLogoClick = () => {
    logoInputRef.current?.click();
  };

  const handleFaviconClick = () => {
    faviconInputRef.current?.click();
  };

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLogoError("");
    const file = e.target.files?.[0];
    if (!file) return;
    const validTypes = [
      "image/jpeg",
      "image/png",
      "image/svg+xml",
      "image/gif",
    ];
    if (!validTypes.includes(file.type)) {
      setLogoError("Invalid file type. Allowed: JPG, PNG, SVG, GIF.");
      setLogoFile(null);
      return;
    }
    if (file.size > 1024 * 1024) {
      setLogoError("File too large. Max 1MB.");
      setLogoFile(null);
      return;
    }
    // Check dimensions
    const img = new window.Image();
    img.onload = function () {
      if (img.width > 720 || img.height > 720) {
        setLogoError("Image dimensions must not exceed 720px.");
        setLogoFile(null);
      } else {
        setLogoFile(file);
      }
    };
    img.onerror = function () {
      setLogoError("Invalid image file.");
      setLogoFile(null);
    };
    img.src = URL.createObjectURL(file);
  };

  const handleFaviconChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFaviconError("");
    const file = e.target.files?.[0];
    if (!file) return;
    const validTypes = [
      "image/png",
      "image/jpeg",
      "image/x-icon",
      "image/vnd.microsoft.icon",
    ];
    if (!validTypes.includes(file.type)) {
      setFaviconError("Invalid file type. Allowed: PNG, JPG, ICO.");
      setFaviconFile(null);
      return;
    }
    if (file.size > 100 * 1024) {
      setFaviconError("File too large. Max 100KB.");
      setFaviconFile(null);
      return;
    }
    // Check dimensions and squareness
    const img = new window.Image();
    img.onload = function () {
      if (img.width !== img.height) {
        setFaviconError("Favicon must be square.");
        setFaviconFile(null);
      } else if (!(img.width === 32 || img.width === 48)) {
        setFaviconError("Favicon must be 32x32 or 48x48.");
        setFaviconFile(null);
      } else {
        setFaviconFile(file);
      }
    };
    img.onerror = function () {
      setFaviconError("Invalid image file.");
      setFaviconFile(null);
    };
    img.src = URL.createObjectURL(file);
  };

  const validateForm = () => {
    if (!form.domain.trim()) return "Domain is required.";
    if (!/^([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$/.test(form.domain.trim()))
      return "Invalid domain format.";
    if (!form.siteName.trim()) return "Site Name is required.";
    if (!form.tagline.trim()) return "Tagline is required.";
    if (!logoFile) return "Logo is required.";
    if (logoError) return logoError;
    if (!faviconFile) return "Favicon is required.";
    if (faviconError) return faviconError;
    if (companyTab === "existing" && !form.company)
      return "Company Name is required.";
    if (companyTab === "new" && !form.companyName.trim())
      return "Company Name is required.";
    if (!form.phone.trim()) return "Phone number is required.";
    if (!/^\+?[0-9\-\s]{7,}$/.test(form.phone.trim()))
      return "Invalid phone number.";
    if (!form.email.trim()) return "Email is required.";
    if (!/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(form.email.trim()))
      return "Invalid email.";
    if (!form.address.trim()) return "Address is required.";
    return "";
  };

  const percentage = Math.round(((step - 1) / (totalSteps - 1)) * 100);

  return (
    <div>
      {/* Stepper */}
      <SiteStepper currentStep={1} />

      {/* Main Card */}
      <form onSubmit={handleSubmit}>
        <Card className="bg-white mx-auto p-0 gap-0">
          <h2 className="text-xl font-semibold border-b border-gray-200 px-6 py-4">
            Add Site Details
          </h2>
          <div className="p-6">
            <div className="grid grid-cols-2 xl:grid-cols-12 gap-6 mb-6">
              <div className="grid gap-2 col-span-2 md:col-span-2 xl:col-span-6">
                <Label className="gap-1" htmlFor="domain">
                  URL/Domain Name{" "}
                  <span className="text-xs text-gray-500">
                    (e.g. example.com)
                  </span>
                </Label>
                <Input
                  id="domain"
                  placeholder="URL/Domain"
                  value={form.domain}
                  onChange={handleInputChange}
                />
              </div>
              <div className="grid gap-2 col-span-2  md:col-span-1 xl:col-span-3">
                <Label className="gap-1" htmlFor="siteName">
                  Site Name/Brand
                </Label>
                <Input
                  id="siteName"
                  placeholder="Site Name/Brand"
                  value={form.siteName}
                  onChange={handleInputChange}
                />
              </div>
              <div className="grid gap-2 col-span-2  md:col-span-1 xl:col-span-3">
                <Label className="gap-1" htmlFor="tagline">
                  Site Tagline
                </Label>
                <Input
                  id="tagline"
                  placeholder="Site Tagline"
                  value={form.tagline}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
              {/* Upload Logo */}
              <div className="grid gap-2">
                <Label className="gap-1" htmlFor="logo">
                  Upload Logo
                </Label>
                <div className="border-2 border-dashed rounded-lg p-6 flex flex-col items-center min-h-[100px]">
                  <div className="flex flex-col items-center">
                    <div className="font-semibold mb-4 flex items-center gap-3">
                      <Image
                        src={uploadIcon}
                        alt="Upload Icon"
                        width={30}
                        height={30}
                      />
                      <span className="flex gap-2">
                        Drag and drop or
                        <span
                          className="text-cyan-600 cursor-pointer"
                          onClick={handleLogoClick}
                        >
                          Click here
                        </span>
                      </span>
                    </div>
                    <span className="text-xs text-yellow-700 bg-[#FEF3CD] rounded px-2 py-2 mb-2 text-center">
                      Supported formats: JPG, PNG, SVG, or GIF, with a max file
                      size of 1MB and dimensions not exceeding 720px.
                    </span>
                    <input
                      type="file"
                      accept=".jpg,.jpeg,.png,.svg,.gif"
                      style={{ display: "none" }}
                      ref={logoInputRef}
                      onChange={handleLogoChange}
                    />
                    {logoFile && (
                      <span className="text-xs text-gray-500">
                        {logoFile.name}
                      </span>
                    )}
                    {logoError && (
                      <span className="text-xs text-red-500 mt-1">
                        {logoError}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              {/* Upload Favicon */}
              <div className="grid gap-2">
                <Label className="gap-1" htmlFor="logo">
                  Upload Favicon
                </Label>
                <div className="border-2 border-dashed rounded-lg p-6 flex flex-col items-center min-h-[100px]">
                  <div className="flex flex-col items-center">
                    <div className="font-semibold mb-4 flex items-center gap-3">
                      <Image
                        src={uploadIcon}
                        alt="Upload Icon"
                        width={30}
                        height={30}
                      />
                      <span className="flex gap-2">
                        Drag and drop or
                        <span
                          className="text-cyan-600 cursor-pointer"
                          onClick={handleFaviconClick}
                        >
                          Click here
                        </span>
                      </span>
                    </div>
                    <span className="text-xs text-yellow-700 bg-[#FEF3CD] rounded px-2 py-2 mb-2 text-center">
                      Use a square PNG, JPG, or ICO file (32×32 or 48×48), with
                      a maximum size of 100KB.
                    </span>
                    <input
                      type="file"
                      accept=".png,.jpg,.jpeg,.ico"
                      style={{ display: "none" }}
                      ref={faviconInputRef}
                      onChange={handleFaviconChange}
                    />
                    {faviconFile && (
                      <span className="text-xs text-gray-500">
                        {faviconFile.name}
                      </span>
                    )}
                    {faviconError && (
                      <span className="text-xs text-red-500 mt-1">
                        {faviconError}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
            {/* Company Info */}
            <h3 className="text-lg font-bold mb-4">Company Information</h3>
            <Tabs
              value={companyTab}
              onValueChange={setCompanyTab}
              className="mb-6"
            >
              <TabsList className="mb-4 companty-tabs border-gray-300">
                <TabsTrigger value="existing" className="w-[130px]">
                  Existing Company
                </TabsTrigger>
                <TabsTrigger value="new" className="w-[130px]">
                  New Company
                </TabsTrigger>
              </TabsList>
              {companyTab === "existing" ? (
                <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
                  <div className="grid gap-2 lg:col-span-6 xl:col-span-3">
                    <Label className="gap-1" htmlFor="company">
                      Company
                    </Label>
                    <Select
                      value={form.company}
                      onValueChange={(selectedCompany) => {
                        const selected = companies.find(
                          (c) => c.company === selectedCompany
                        );
                        if (selected) {
                          setForm((f) => ({
                            ...f,
                            company: selected.company,
                            phone: selected.phone || "",
                            email: selected.email || "",
                            address: selected.address || "",
                          }));
                        }
                      }}
                    >
                      <SelectTrigger id="company" className="w-full">
                        <SelectValue placeholder="Select Existing Company" />
                      </SelectTrigger>
                      <SelectContent className="w-full bg-white">
                        {companies.map((c) => (
                          <SelectItem
                            key={c.id}
                            className="cursor-pointer hover:bg-gray-100 data-[state=checked]:bg-cyan-600 data-[state=checked]:text-white focus:bg-gray-100"
                            value={c.company}
                          >
                            {c.company}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid gap-2 lg:col-span-6 xl:col-span-3">
                    <Label className="gap-1" htmlFor="phone">
                      Phone Number
                    </Label>
                    <Input
                      id="phone"
                      type="number"
                      placeholder="Phone number"
                      value={form.phone}
                      onChange={handleInputChange}
                      className="[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                    />
                  </div>
                  <div className="grid gap-2 lg:col-span-12 xl:col-span-6">
                    <Label className="gap-1" htmlFor="email">
                      Email
                    </Label>
                    <Input
                      id="email"
                      placeholder="Email"
                      value={form.email}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="grid gap-2 lg:col-span-12 xl:col-span-12">
                    <Label className="gap-1" htmlFor="address">
                      Address
                    </Label>
                    <Input
                      id="address"
                      placeholder="Address"
                      value={form.address}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>
              ) : (
                <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
                  <div className="grid gap-2 lg:col-span-6 xl:col-span-3">
                    <Label className="gap-1" htmlFor="companyName">
                      Company Name
                    </Label>
                    <Input
                      id="companyName"
                      placeholder="Company Name"
                      value={form.companyName}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="grid gap-2 lg:col-span-6 xl:col-span-3">
                    <Label className="gap-1" htmlFor="phone">
                      Phone Number
                    </Label>
                    <Input
                      id="phone"
                      type="number"
                      className="[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                      placeholder="Phone number"
                      value={form.phone}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="grid gap-2 lg:col-span-12 xl:col-span-6">
                    <Label className="gap-1" htmlFor="email">
                      Email
                    </Label>
                    <Input
                      id="email"
                      placeholder="Email"
                      value={form.email}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="grid gap-2 lg:col-span-12 xl:col-span-12">
                    <Label className="gap-1" htmlFor="address">
                      Address
                    </Label>
                    <Input
                      id="address"
                      placeholder="Address"
                      value={form.address}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>
              )}
            </Tabs>
            {formError && <div className="text-red-500 mb-4">{formError}</div>}
          </div>
        </Card>
        <div className="action-footer mt-6 flex justify-end">
          <div className=" flex justify-end gap-4">
            <Button
              type="submit"
              className="text-white flex items-center gap-2"
              disabled={submitting}
            >
              {submitting ? (
                "Submitting..."
              ) : (
                <>
                  Next Step
                  <Image src={arrowRightIcon} alt="Upload Icon" />
                </>
              )}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
}
